/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as auth from "../auth.js";
import type * as gmailActions from "../gmailActions.js";
import type * as http from "../http.js";
import type * as monacoActions from "../monacoActions.js";
import type * as queries from "../queries.js";
import type * as router from "../router.js";
import type * as search from "../search.js";
import type * as search1back from "../search1back.js";
import type * as search2bacup from "../search2bacup.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  auth: typeof auth;
  gmailActions: typeof gmailActions;
  http: typeof http;
  monacoActions: typeof monacoActions;
  queries: typeof queries;
  router: typeof router;
  search: typeof search;
  search1back: typeof search1back;
  search2bacup: typeof search2bacup;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
