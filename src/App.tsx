import { Authenticated, Unauthenticated, useQuery, useConvexAuth, useAction } from "convex/react";
import { api } from "../convex/_generated/api";
import { SignInForm } from "./SignInForm";
import { SignOutButton } from "./SignOutButton";
import { Toaster } from "sonner";
import { ChatInterface } from "./ChatInterface";
import { Sidebar } from "./Sidebar";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { AnimatedHamburger } from "./components/AnimatedIcons";
import { AnimatedSpinner } from "./components/AnimatedIcons";
import { PageTransition, HoverScale } from "./components/PageTransition";
import { CookieConsent, useCookieConsent } from "./components/CookieConsent";
//import router from "react-router-dom";
import { Route, Routes, Navigate } from "react-router-dom";
import MonacoChatbot from "./monaco-chatbot-app";

// export default function App() {
//   return <MainApp />;
// }

export default  function App() {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [sidebarExpanded, setSidebarExpanded] = useState(true);
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);
  const { isAuthenticated, isLoading } = useConvexAuth();
  const loggedInUser = useQuery(api.auth.loggedInUser);
  const handleCallbackWithUserId = useAction(api.gmailActions.handleGmailCallbackWithUserId);
  const { hasAccepted, acceptCookies } = useCookieConsent();

  // Calculate if we should apply sidebar positioning (only for authenticated users)
  const shouldApplySidebarPositioning = isAuthenticated;

  // Log authentication state changes
  useEffect(() => {
    console.log('[MAIN APP] Authentication state changed:', {
      isAuthenticated,
      isLoading,
      loggedInUser: loggedInUser ? {
        id: loggedInUser._id,
        email: loggedInUser.email || 'no-email',
        name: loggedInUser.name || 'no-name',
        keys: Object.keys(loggedInUser)
      } : null,
      url: window.location.href,
      timestamp: new Date().toISOString()
    });

    // Check for OAuth return
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const oauthInProgress = localStorage.getItem('gmail_oauth_in_progress');

    if (oauthInProgress && code) {
      console.log('[MAIN APP] OAuth return detected:', {
        isAuthenticated,
        isLoading,
        loggedInUser: loggedInUser ? { id: loggedInUser._id } : null,
        oauthTimestamp: localStorage.getItem('gmail_oauth_timestamp'),
        storedUserId: localStorage.getItem('gmail_oauth_user_id')
      });
    }
  }, [isAuthenticated, isLoading, loggedInUser]);

  // Handle OAuth callback processing regardless of authentication state
  useEffect(() => {
    const processOAuthCallback = async () => {
      const urlParams = new URLSearchParams(window.location.search);
      const code = urlParams.get('code');
      const error = urlParams.get('error');
      const oauthInProgress = localStorage.getItem('gmail_oauth_in_progress');
      const storedUserId = localStorage.getItem('gmail_oauth_user_id');

      console.log('[MAIN APP] Checking for OAuth callback...', {
        hasCode: !!code,
        hasError: !!error,
        oauthInProgress,
        storedUserId,
        isAuthenticated,
        isLoading
      });

      if (oauthInProgress && storedUserId && (code || error)) {
        console.log('[MAIN APP] Processing OAuth callback...');

        // Check if the stored user was anonymous (additional safety check)
        const storedUserEmail = localStorage.getItem('gmail_oauth_user_email');
        if (storedUserEmail === 'anonymous') {
          console.error('[MAIN APP] OAuth initiated by anonymous user, blocking callback');
          toast.error('Gmail integration requires a registered account. Please sign up with an email address.');

          // Clean up OAuth state
          localStorage.removeItem('gmail_oauth_in_progress');
          localStorage.removeItem('gmail_oauth_user_id');
          localStorage.removeItem('gmail_oauth_user_email');
          localStorage.removeItem('gmail_oauth_timestamp');
          localStorage.removeItem('gmail_oauth_return_path');

          // Clean up URL
          window.history.replaceState({}, document.title, '/');
          return;
        }

        if (error) {
          console.error('[MAIN APP] OAuth error:', error);
          toast.error(`Gmail authentication failed: ${error}`);

          // Clean up OAuth state
          localStorage.removeItem('gmail_oauth_in_progress');
          localStorage.removeItem('gmail_oauth_user_id');
          localStorage.removeItem('gmail_oauth_user_email');
          localStorage.removeItem('gmail_oauth_timestamp');
          localStorage.removeItem('gmail_oauth_return_path');

          // Clean up URL
          window.history.replaceState({}, document.title, '/');
          return;
        }

        if (code) {
          try {
            console.log('[MAIN APP] Processing authorization code with stored user ID...', {
              storedUserId,
              codeLength: code.length
            });

            const result = await handleCallbackWithUserId({
              code,
              userId: storedUserId
            });

            console.log('[MAIN APP] OAuth callback successful:', result);
            toast.success('Gmail account connected successfully!');

            // Clean up OAuth state
            localStorage.removeItem('gmail_oauth_in_progress');
            localStorage.removeItem('gmail_oauth_user_id');
            localStorage.removeItem('gmail_oauth_user_email');
            localStorage.removeItem('gmail_oauth_timestamp');
            const returnPath = localStorage.getItem('gmail_oauth_return_path') || '/';
            localStorage.removeItem('gmail_oauth_return_path');

            // Clean up URL and redirect to settings
            window.history.replaceState({}, document.title, returnPath);

          } catch (error) {
            console.error('[MAIN APP] OAuth callback failed:', error);
            toast.error(error instanceof Error ? error.message : 'Failed to connect Gmail account');

            // Clean up OAuth state
            localStorage.removeItem('gmail_oauth_in_progress');
            localStorage.removeItem('gmail_oauth_user_id');
            localStorage.removeItem('gmail_oauth_user_email');
            localStorage.removeItem('gmail_oauth_timestamp');
            localStorage.removeItem('gmail_oauth_return_path');

            // Clean up URL
            window.history.replaceState({}, document.title, '/');
          }
        }
      }
    };

    // Process OAuth callback immediately when component mounts
    processOAuthCallback();
  }, [handleCallbackWithUserId]); // Only depend on the action, not auth state

  return (
    <div className="h-screen relative overflow-hidden" style={{ background: 'linear-gradient(135deg, #FEFCFA 0%, #FBF7F2 50%, #F5F0E8 100%)' }}>
      

      {/* Cookie Consent Modal */}
      <CookieConsent
        onAccept={acceptCookies}
        onSettings={() => toast.info('Settings functionality coming soon!')}
      />

      <Authenticated>
        <Sidebar
          isOpen={sidebarOpen}
          onToggle={() => setSidebarOpen(!sidebarOpen)}
          isExpanded={sidebarExpanded}
          onToggleExpanded={() => setSidebarExpanded(!sidebarExpanded)}
          onSelectSession={(sessionId) => setSelectedSessionId(sessionId)}
          selectedSessionId={selectedSessionId}
        />
      </Authenticated>

      <div className={`absolute top-0 right-0 bottom-0 transition-all duration-300 ease-in-out overflow-hidden ${
        shouldApplySidebarPositioning ? (
          // Authenticated users: apply sidebar positioning
          `${sidebarOpen ? 'left-80' : 'left-0'} ${sidebarExpanded ? 'lg:left-64' : 'lg:left-0'}`
        ) : (
          // Unauthenticated users: full width
          'left-0'
        )
      }`}>
        {/* Header with warm orange background - Fixed Overlay */}
        <header className={`absolute top-0 left-0 right-0 z-20 h-16 flex justify-between items-center px-6 backdrop-blur-md ${
          shouldApplySidebarPositioning ? '' : 'hidden'
        }`} style={{ backgroundColor: 'rgba(255, 149, 102, 0.8)' }}>
          <div className="flex items-center gap-4">
            <Authenticated>
              {/* Mobile hamburger - always visible on mobile */}
              <div className="lg:hidden">
                <AnimatedHamburger
                  isOpen={sidebarOpen}
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                  className="p-2 hover:bg-black/10 transition-colors duration-200"
                  size={20}
                  color="#000000"
                />
              </div>
              {/* Desktop expand button - only show when sidebar is collapsed */}
              {!sidebarExpanded && (
                <div className="hidden lg:block">
                  <AnimatedHamburger
                    isOpen={false}
                    onClick={() => setSidebarExpanded(true)}
                    className="p-2 hover:bg-black/10 transition-colors duration-200"
                    size={20}
                    color="#000000"
                  />
                </div>
              )}
            </Authenticated>
            <div className="flex items-center gap-3">
              <HoverScale scale={1.1} duration={0.2}>
                <div className="w-8 h-8 flex items-center justify-center" style={{ backgroundColor: '#000000', borderRadius: '20px' }}>
                  <span className="text-white font-black text-lg">Q</span>
                </div>
              </HoverScale>
              <h2 className="text-xl text-black tracking-tight" style={{ fontFamily: 'Forum, serif', fontWeight: '400' }}>Quasari</h2>
            </div>
          </div>
          <Authenticated>
            <SignOutButton />
          </Authenticated>
        </header>

        {/* Main Content - Full Height */}
        <main className="h-full flex flex-col overflow-hidden">
          <Content selectedSessionId={selectedSessionId} />
        </main>
      </div>

      <Toaster />
    </div>
  );
}

function Content({ selectedSessionId }: { selectedSessionId: string | null }) {
  const loggedInUser = useQuery(api.auth.loggedInUser);
  const sessions = useQuery(api.queries.getUserSessions) || [];

  if (loggedInUser === undefined) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <AnimatedSpinner size={48} color="#000000" speed={1.5} />
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <Authenticated>
        <Routes>
          <Route
            path="/"
            element={
              <ChatInterface
                hasSearchHistory={sessions.length > 0}
                selectedSessionId={selectedSessionId}
              />
            }
          />
          <Route path="/monaco" element={<MonacoChatbot />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Authenticated>
      <Unauthenticated>
        <div className="h-full flex items-center justify-center p-8 overflow-y-auto">
          <PageTransition direction="fade" duration={0.8} delay={0.2}>
            <div className="text-center max-w-2xl mx-auto">
              <div className="mb-12">
                <PageTransition direction="up" duration={0.6} delay={0.4}>
                  <h1 className="quasari-heading text-4xl md:text-5xl lg:text-6xl mb-8 mx-auto max-w-3xl leading-none" style={{ fontFamily: 'Forum, serif', fontWeight: '400' }}>
                    THE EASIEST WAY<br />
                    TO <span className="relative inline-block">
                      SEARCH
                      <div className="absolute -bottom-2 left-0 right-0 h-3 md:h-4" style={{ backgroundColor: '#FFCBA8' }}></div>
                    </span> THE WEB
                  </h1>
                </PageTransition>
                <PageTransition direction="up" duration={0.6} delay={0.6}>
                  <p className="text-lg md:text-xl mb-12 max-w-xl mx-auto leading-relaxed" style={{ fontFamily: 'Forum, serif', fontWeight: '400', color: '#6F5A3E' }}>
                    Sign in to start searching the web with AI-powered insights
                  </p>
                </PageTransition>
              </div>
              <PageTransition direction="scale" duration={0.5} delay={0.8}>
                <div className="max-w-md mx-auto quasari-card p-8">
                  <SignInForm />
                </div>
              </PageTransition>
            </div>
          </PageTransition>
        </div>
      </Unauthenticated>
    </div>
  );
}
