import React, { useState, useRef, useEffect } from 'react';
import { Send, Play, Code, MessageCircle, Sparkles, Copy, Download } from 'lucide-react';
import { useAction } from "convex/react";
import { api } from "../convex/_generated/api";

// Type definitions
interface Message {
  role: 'user' | 'assistant';
  content: string;
}

interface MonacoEditor {
  getValue(): string;
  setValue(value: string): void;
  onDidChangeModelContent(callback: () => void): { dispose(): void };
  dispose(): void;
}

interface Monaco {
  editor: {
    create(element: HTMLElement, options: any): MonacoEditor;
  };
}

declare global {
  interface Window {
    monaco: Monaco;
    require: {
      config(options: any): void;
      (modules: string[], callback: () => void): void;
    };
  }
}

const MonacoChatbot = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [code, setCode] = useState('');
  const [output, setOutput] = useState('');
  const [activeTab, setActiveTab] = useState('chat');

  // Debug logging for tab changes
  useEffect(() => {
    console.log('[MONACO DEBUG] Active tab changed to:', activeTab);
  }, [activeTab]);
  const [sessionId] = useState(() => `monaco-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`);
  const [lastError, setLastError] = useState<string>('');
  const [showErrorFix, setShowErrorFix] = useState(false);
  const monacoRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<MonacoEditor | null>(null);

  // Convex actions
  const generateAndExecuteCode = useAction(api.monacoActions.generateAndExecuteCode);
  const generateTestApp = useAction(api.monacoActions.generateTestApp);
  const validateCode = useAction(api.monacoActions.validateCode);

  // Initialize Monaco Editor
  useEffect(() => {
    const initMonaco = async () => {
      // Load Monaco Editor from CDN
      const script = document.createElement('script');
      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.44.0/min/vs/loader.min.js';
      script.onload = () => {
        window.require.config({ paths: { 'vs': 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.44.0/min/vs' }});
        window.require(['vs/editor/editor.main'], () => {
          if (monacoRef.current && !editorRef.current) {
            editorRef.current = window.monaco.editor.create(monacoRef.current, {
              value: '// Ask the AI to generate some code!\n// Example: "Create a todo list app"\n\nfunction App() {\n  return (\n    <div>\n      <h1>Hello World!</h1>\n    </div>\n  );\n}',
              language: 'javascript',
              theme: 'vs-dark',
              automaticLayout: true,
              minimap: { enabled: false },
              fontSize: 14,
              lineNumbers: 'on',
              scrollBeyondLastLine: false,
              wordWrap: 'on'
            });

            editorRef.current.onDidChangeModelContent(() => {
              if (editorRef.current) {
                setCode(editorRef.current.getValue());
              }
            });
          }
        });
      };
      document.head.appendChild(script);
    };

    initMonaco();

    return () => {
      if (editorRef.current) {
        editorRef.current.dispose();
      }
    };
  }, []);

  const callBackendAPI = async (prompt: string, errorContext?: string): Promise<{
    code: string;
    htmlPreview?: string;
    explanation?: string;
    iterations?: number;
    finalStatus?: string;
    errorHistory?: Array<{iteration: number; error: string; fix: string}>;
  }> => {
    try {
      console.log('[MONACO FRONTEND] Using generateAndExecuteCode action');
      const result = await generateAndExecuteCode({
        prompt,
        conversationHistory: messages,
        sessionId,
      });

      // If we have an HTML preview, automatically show it
      if (result.htmlPreview) {
        setOutput(result.htmlPreview);
        setActiveTab('preview');
      }

      return {
        code: result.code,
        htmlPreview: result.htmlPreview,
        explanation: result.explanation
      };
    } catch (error) {
      throw new Error(`Backend API Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleFixError = async () => {
    if (!lastError || !input.trim()) {
      const errorMessage: Message = {
        role: 'assistant',
        content: 'Please describe the error you encountered or provide more details about what needs to be fixed.'
      };
      setMessages(prev => [...prev, errorMessage]);
      return;
    }

    setIsLoading(true);
    try {
      const aiResponse = await callBackendAPI(input, lastError);

      // Create a comprehensive message with error correction info
      let messageContent = aiResponse.explanation || "Code error correction completed!";
      if (aiResponse.iterations) {
        messageContent += `\n\n🔧 Fixed after ${aiResponse.iterations} iteration(s)`;
        messageContent += `\n📊 Status: ${aiResponse.finalStatus}`;
      }
      if (aiResponse.errorHistory && aiResponse.errorHistory.length > 0) {
        messageContent += `\n\n📝 Error History:\n${aiResponse.errorHistory.map(h => `• Iteration ${h.iteration}: ${h.error}`).join('\n')}`;
      }

      const aiMessage: Message = { role: 'assistant', content: messageContent };
      setMessages(prev => [...prev, aiMessage]);

      // Always update the editor with the corrected code
      if (aiResponse.code) {
        if (editorRef.current) {
          editorRef.current.setValue(aiResponse.code);
        }
        setCode(aiResponse.code);

        // Show code view first, then preview if available
        if (aiResponse.htmlPreview) {
          setActiveTab('codeview');
          setTimeout(() => setActiveTab('preview'), 2000);
        } else {
          setActiveTab('codeview');
        }
      }

      // Clear error state after successful fix
      setLastError('');
      setShowErrorFix(false);
      setInput('');
    } catch (error) {
      const errorMessage: Message = {
        role: 'assistant',
        content: `Error correction failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const detectErrorFromPreview = async (htmlContent: string, originalPrompt: string) => {
    // Check if the HTML content contains error indicators
    if (htmlContent.includes('Execution Error:') ||
        htmlContent.includes('error') ||
        htmlContent.includes('undefined') ||
        htmlContent.includes('ReferenceError') ||
        htmlContent.includes('TypeError') ||
        htmlContent.includes('SyntaxError') ||
        htmlContent.includes("Can't find variable")) {

      // Extract error message
      let errorText = '';
      const errorMatch = htmlContent.match(/Execution Error:<\/strong><br>([^<]+)/);
      if (errorMatch) {
        errorText = errorMatch[1].trim();
      } else {
        // Try to extract from other error patterns
        const patterns = [
          /Can't find variable: (\w+)/,
          /ReferenceError: (\w+) is not defined/,
          /TypeError: (.+)/,
          /SyntaxError: (.+)/
        ];

        for (const pattern of patterns) {
          const match = htmlContent.match(pattern);
          if (match) {
            errorText = match[0];
            break;
          }
        }
      }

      if (errorText) {
        console.log('[MONACO FRONTEND] Error detected, auto-fixing:', errorText);
        setLastError(errorText);
        setShowErrorFix(true);

        // Automatically attempt to fix the error
        await autoFixError(originalPrompt, errorText);
      }
    } else {
      // Clear error state if no errors detected
      setLastError('');
      setShowErrorFix(false);
    }
  };

  const autoFixError = async (originalPrompt: string, errorText: string) => {
    console.log('[MONACO FRONTEND] Auto-fixing error:', errorText);

    // Add a message about auto-fixing
    const autoFixMessage: Message = {
      role: 'assistant',
      content: `🔧 Error detected: "${errorText}"\n\nAutomatically attempting to fix this error...`
    };
    setMessages(prev => [...prev, autoFixMessage]);

    setIsLoading(true);
    try {
      const aiResponse = await callBackendAPI(originalPrompt, errorText);

      // Create a comprehensive message with error correction info
      let messageContent = aiResponse.explanation || "Code error correction completed!";
      if (aiResponse.iterations) {
        messageContent += `\n\n🔧 Auto-fixed after ${aiResponse.iterations} iteration(s)`;
        messageContent += `\n📊 Status: ${aiResponse.finalStatus}`;
      }
      if (aiResponse.errorHistory && aiResponse.errorHistory.length > 0) {
        messageContent += `\n\n📝 Error History:\n${aiResponse.errorHistory.map(h => `• Iteration ${h.iteration}: ${h.error}`).join('\n')}`;
      }

      const aiMessage: Message = { role: 'assistant', content: messageContent };
      setMessages(prev => [...prev, aiMessage]);

      // Always update the editor with the corrected code
      if (aiResponse.code) {
        if (editorRef.current) {
          editorRef.current.setValue(aiResponse.code);
        }
        setCode(aiResponse.code);

        // Show code view first, then preview if available
        if (aiResponse.htmlPreview) {
          setOutput(aiResponse.htmlPreview);
          setActiveTab('codeview');

          // Switch to preview after showing the corrected code
          setTimeout(() => {
            setActiveTab('preview');

            // Check if the fix worked after a delay
            setTimeout(() => {
              if (!aiResponse.htmlPreview?.includes('Execution Error:') &&
                  !aiResponse.htmlPreview?.includes("Can't find variable")) {
                setLastError('');
                setShowErrorFix(false);
                console.log('[MONACO FRONTEND] Error successfully auto-fixed!');
              }
            }, 1000);
          }, 2000);
        } else {
          setActiveTab('codeview');
        }
      }
    } catch (error) {
      const errorMessage: Message = {
        role: 'assistant',
        content: `❌ Auto-fix failed: ${error instanceof Error ? error.message : 'Unknown error'}\n\nPlease describe what you'd like to fix manually.`
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!input.trim()) return;

    const userMessage: Message = { role: 'user', content: input };
    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      const aiResponse = await callBackendAPI(input);

      // Create a comprehensive message with explanation and code info
      let messageContent = aiResponse.explanation || "Code generated successfully!";
      if (aiResponse.code) {
        messageContent += `\n\n✅ Generated ${aiResponse.code.length} characters of code`;
        if (aiResponse.htmlPreview) {
          messageContent += "\n🚀 Application is now running in the preview panel";
        }
      }

      const aiMessage: Message = { role: 'assistant', content: messageContent };
      setMessages(prev => [...prev, aiMessage]);

      // Always update the editor with the generated code
      if (aiResponse.code) {
        if (editorRef.current) {
          editorRef.current.setValue(aiResponse.code);
        }
        setCode(aiResponse.code);

        // Show code view first, then preview if available
        if (aiResponse.htmlPreview) {
          setActiveTab('codeview');
          // Switch to preview after showing code for a moment
          setTimeout(() => {
            setActiveTab('preview');
            // Detect errors in the preview after a short delay
            setTimeout(() => {
              detectErrorFromPreview(aiResponse.htmlPreview || '', input);
            }, 1000);
          }, 2000);
        } else {
          setActiveTab('codeview');
        }
      }
    } catch (error) {
      const errorMessage: Message = {
        role: 'assistant',
        content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const runCode = () => {
    try {
      // Create a safe execution environment
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      document.body.appendChild(iframe);

      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

      if (!iframeDoc) {
        throw new Error('Unable to access iframe document');
      }

      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
          <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
          <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
          <style>
            body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
            .error { color: red; background: #ffebee; padding: 10px; border-radius: 4px; }
          </style>
        </head>
        <body>
          <div id="root"></div>
          <script type="text/babel">
            try {
              ${code}

              // Try to render the component
              const root = ReactDOM.createRoot(document.getElementById('root'));
              if (typeof App !== 'undefined') {
                root.render(<App />);
              } else {
                root.render(<div>Code executed successfully! Define an 'App' component to see the result.</div>);
              }
            } catch (error) {
              document.getElementById('root').innerHTML =
                '<div class="error"><strong>Error:</strong> ' + error.message + '</div>';
            }
          </script>
        </body>
        </html>
      `;

      iframeDoc.open();
      iframeDoc.write(htmlContent);
      iframeDoc.close();

      // Wait for execution and capture result
      setTimeout(() => {
        const result = iframeDoc.body.innerHTML;
        setOutput(result);
        setActiveTab('preview');
        document.body.removeChild(iframe);
      }, 1000);

    } catch (error) {
      setOutput(`<div class="error">Error: ${error instanceof Error ? error.message : 'Unknown error'}</div>`);
      setActiveTab('preview');
    }
  };

  const copyCode = () => {
    navigator.clipboard.writeText(code);
  };

  const handleTestApp = async () => {
    setIsLoading(true);
    try {
      const result = await generateTestApp();

      // Update the editor and preview with test app
      if (editorRef.current) {
        editorRef.current.setValue(result.code);
      }
      setCode(result.code);
      setOutput(result.htmlPreview);
      setActiveTab('preview');

      const testMessage: Message = {
        role: 'assistant',
        content: result.explanation + "\n\n🧪 Test application loaded successfully!"
      };
      setMessages(prev => [...prev, testMessage]);
    } catch (error) {
      const errorMessage: Message = {
        role: 'assistant',
        content: `Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const forceErrorCorrection = async (prompt: string, errorText: string) => {
    console.log('[MONACO FRONTEND] Forcing error correction for:', errorText);

    setIsLoading(true);
    try {
      const aiResponse = await callBackendAPI(prompt, errorText);

      // Create a comprehensive message with error correction info
      let messageContent = `🔧 Error correction completed!\n\n${aiResponse.explanation || "Code has been fixed."}`;
      if (aiResponse.iterations) {
        messageContent += `\n\n📊 Iterations: ${aiResponse.iterations}`;
        messageContent += `\n📈 Status: ${aiResponse.finalStatus}`;
      }

      const aiMessage: Message = { role: 'assistant', content: messageContent };
      setMessages(prev => [...prev, aiMessage]);

      // Update the editor and preview
      if (aiResponse.code) {
        if (editorRef.current) {
          editorRef.current.setValue(aiResponse.code);
        }
        setCode(aiResponse.code);

        if (aiResponse.htmlPreview) {
          setOutput(aiResponse.htmlPreview);
          setActiveTab('codeview');
          // Switch to preview after showing code
          setTimeout(() => setActiveTab('preview'), 2000);
        } else {
          setActiveTab('codeview');
        }
      }
    } catch (error) {
      const errorMessage: Message = {
        role: 'assistant',
        content: `Error correction failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const downloadCode = () => {
    const blob = new Blob([code], { type: 'text/javascript' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'generated-code.js';
    a.click();
    URL.revokeObjectURL(url);
  };

  const highlightCode = (code: string) => {
    // Simple syntax highlighting for JavaScript/React
    return code
      .replace(/(function|const|let|var|if|else|for|while|return|import|export|class|extends|async|await)\b/g, '<span style="color: #569cd6;">$1</span>')
      .replace(/(React|useState|useEffect|useRef|useCallback|useMemo)\b/g, '<span style="color: #4ec9b0;">$1</span>')
      .replace(/(['"`])((?:\\.|(?!\1)[^\\])*?)\1/g, '<span style="color: #ce9178;">$1$2$1</span>')
      .replace(/(\/\/.*$)/gm, '<span style="color: #6a9955;">$1</span>')
      .replace(/(\/\*[\s\S]*?\*\/)/g, '<span style="color: #6a9955;">$1</span>')
      .replace(/(\{|\}|\[|\]|\(|\))/g, '<span style="color: #ffd700;">$1</span>')
      .replace(/(<\/?[a-zA-Z][^>]*>)/g, '<span style="color: #92c5f8;">$1</span>');
  };

  const formatCodeWithLineNumbers = (code: string) => {
    const lines = code.split('\n');
    const maxLineNumber = lines.length;
    const lineNumberWidth = maxLineNumber.toString().length;

    return lines.map((line, index) => {
      const lineNumber = (index + 1).toString().padStart(lineNumberWidth, ' ');
      const highlightedLine = highlightCode(line);
      return `<div class="flex"><span class="text-gray-500 select-none mr-4 text-right" style="min-width: ${lineNumberWidth + 1}ch;">${lineNumber}</span><span class="flex-1">${highlightedLine}</span></div>`;
    }).join('');
  };

  const validateCodeHandler = async () => {
    if (!code.trim()) {
      const validationMessage: Message = {
        role: 'assistant',
        content: 'No code to validate. Please generate or write some code first.'
      };
      setMessages(prev => [...prev, validationMessage]);
      setActiveTab('chat');
      return;
    }

    setIsLoading(true);
    try {
      const validation = await validateCode({
        code,
        sessionId,
      });

      let validationContent = `**Code Validation Results:**\n\n`;
      validationContent += `**Status:** ${validation.isValid ? '✅ Valid' : '❌ Issues Found'}\n\n`;

      if (validation.issues.length > 0) {
        validationContent += `**Issues:**\n${validation.issues.map((issue: string) => `• ${issue}`).join('\n')}\n\n`;
      }

      if (validation.suggestions.length > 0) {
        validationContent += `**Suggestions:**\n${validation.suggestions.map((suggestion: string) => `• ${suggestion}`).join('\n')}`;
      }

      const validationMessage: Message = {
        role: 'assistant',
        content: validationContent
      };
      setMessages(prev => [...prev, validationMessage]);
      setActiveTab('chat');
    } catch (error) {
      const errorMessage: Message = {
        role: 'assistant',
        content: `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
      setMessages(prev => [...prev, errorMessage]);
      setActiveTab('chat');
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <div className="min-h-screen bg-gray-900 flex flex-col">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Sparkles className="text-blue-400" size={24} />
            <h1 className="text-xl font-bold text-white">Monaco AI Chatbot</h1>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex bg-gray-700 rounded-lg p-1 overflow-x-auto">
              <button
                onClick={() => setActiveTab('chat')}
                className={`px-3 py-1 rounded text-sm ${activeTab === 'chat' ? 'bg-blue-600 text-white' : 'text-gray-400 hover:text-white'}`}
              >
                <MessageCircle size={16} className="inline mr-1" />
                Chat
              </button>
              <button
                onClick={() => setActiveTab('codeview')}
                className={`px-3 py-1 rounded text-sm ${activeTab === 'codeview' ? 'bg-blue-600 text-white' : 'text-gray-400 hover:text-white'}`}
              >
                <Code size={16} className="inline mr-1" />
                Code View
              </button>
              <button
                onClick={() => setActiveTab('editor')}
                className={`px-3 py-1 rounded text-sm ${activeTab === 'editor' ? 'bg-blue-600 text-white' : 'text-gray-400 hover:text-white'}`}
              >
                <Sparkles size={16} className="inline mr-1" />
                Editor
              </button>
              <button
                onClick={() => setActiveTab('preview')}
                className={`px-3 py-1 rounded text-sm ${activeTab === 'preview' ? 'bg-blue-600 text-white' : 'text-gray-400 hover:text-white'}`}
              >
                <Play size={16} className="inline mr-1" />
                Preview
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Chat Panel */}
        {activeTab === 'chat' && (
          <div className="flex-1 flex flex-col">
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.length === 0 && (
                <div className="text-center text-gray-400 py-12">
                  <MessageCircle size={48} className="mx-auto mb-4" />
                  <p className="text-lg mb-4">Create beautiful web applications instantly</p>
                  <p className="text-sm mb-6">Describe what you want to build, and I'll generate a complete, working application</p>

                  {/* Quick Start Examples */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-w-4xl mx-auto mb-6">
                    {[
                      "Create a modern todo list app",
                      "Build a weather dashboard",
                      "Make a calculator with history",
                      "Design a landing page",
                      "Create a photo gallery",
                      "Build a chat interface"
                    ].map((example, idx) => (
                      <button
                        key={idx}
                        onClick={() => setInput(example)}
                        className="p-3 bg-gray-800 hover:bg-gray-700 rounded-lg text-sm text-left transition-colors border border-gray-600 hover:border-gray-500"
                      >
                        {example}
                      </button>
                    ))}
                  </div>

                  <div className="flex items-center justify-center space-x-4 mb-4">
                    <button
                      onClick={handleTestApp}
                      className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
                      disabled={isLoading}
                    >
                      🧪 Test System
                    </button>
                    <button
                      onClick={() => {
                        setInput("Create a simple counter app");
                        // Simulate clicking send after setting input
                        setTimeout(() => {
                          if (!isLoading) {
                            handleSendMessage();
                          }
                        }, 100);
                      }}
                      className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
                      disabled={isLoading}
                    >
                      🐛 Test Error Correction
                    </button>
                    <button
                      onClick={() => {
                        console.log('[MONACO DEBUG] Switching to code view, current tab:', activeTab);
                        setActiveTab('codeview');
                        // Add some test code if none exists
                        if (!code) {
                          setCode(`function App() {
  const [count, setCount] = React.useState(0);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">Counter: {count}</h1>
        <button
          onClick={() => setCount(count + 1)}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Increment
        </button>
      </div>
    </div>
  );
}`);
                        }
                      }}
                      className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
                    >
                      <Code size={16} className="inline mr-1" />
                      Test Code View
                    </button>
                  </div>

                  <p className="text-xs text-gray-500">
                    ✨ Apps are generated with React, Tailwind CSS, and run instantly in the preview
                  </p>
                </div>
              )}
              
              {messages.map((msg, idx) => (
                <div key={idx} className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-xs lg:max-w-md xl:max-w-lg p-3 rounded-lg ${
                    msg.role === 'user' 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-gray-700 text-gray-100'
                  }`}>
                    <pre className="whitespace-pre-wrap text-sm font-mono">{msg.content}</pre>
                  </div>
                </div>
              ))}
              
              {isLoading && (
                <div className="flex justify-start">
                  <div className="bg-gray-700 text-gray-100 p-4 rounded-lg max-w-xs">
                    <div className="flex items-center space-x-3">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-400"></div>
                      <div>
                        <div className="font-medium">Generating application...</div>
                        <div className="text-sm text-gray-300">Creating React components with Tailwind CSS</div>
                      </div>
                    </div>
                    <div className="mt-3 bg-gray-600 rounded-full h-1">
                      <div className="bg-blue-400 h-1 rounded-full animate-pulse" style={{width: '60%'}}></div>
                    </div>
                  </div>
                </div>
              )}
            </div>
            
            {/* Error Fix Banner */}
            {showErrorFix && (
              <div className="border-t border-red-600 bg-red-900/20 p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="text-red-400">⚠️</div>
                    <div>
                      <div className="text-red-300 font-medium text-sm">Error Detected in Preview</div>
                      <div className="text-red-400 text-xs">{lastError.substring(0, 80)}...</div>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowErrorFix(false)}
                    className="text-red-400 hover:text-red-300 text-xs"
                  >
                    ✕
                  </button>
                </div>
              </div>
            )}

            {/* Chat Input */}
            <div className="border-t border-gray-700 p-4">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !isLoading) {
                      if (showErrorFix && input.trim()) {
                        handleFixError();
                      } else {
                        handleSendMessage();
                      }
                    }
                  }}
                  placeholder={showErrorFix ? "Describe what you want to fix or change..." : "Describe your app idea... (e.g., 'Create a modern todo app with dark mode')"}
                  className="flex-1 p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-blue-400 focus:outline-none placeholder-gray-400"
                  disabled={isLoading}
                />
                {showErrorFix ? (
                  <button
                    onClick={handleFixError}
                    disabled={!input.trim() || isLoading}
                    className="bg-red-600 text-white px-4 py-3 rounded-lg hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors"
                  >
                    🔧 Fix
                  </button>
                ) : (
                  <button
                    onClick={handleSendMessage}
                    disabled={isLoading || !input.trim()}
                    className="bg-blue-600 text-white p-3 rounded-lg hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors"
                  >
                    <Send size={20} />
                  </button>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Code View Panel */}
        {activeTab === 'codeview' && (
          <div className="flex-1 flex flex-col">
            <div className="bg-gray-800 border-b border-gray-700 p-2 flex items-center justify-between">
              <span className="text-gray-400 text-sm">Generated Code</span>
              <div className="flex space-x-2">
                <button
                  onClick={copyCode}
                  className="bg-gray-700 text-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-600 transition-colors"
                  disabled={!code}
                >
                  <Copy size={16} className="inline mr-1" />
                  Copy Code
                </button>
                <button
                  onClick={downloadCode}
                  className="bg-gray-700 text-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-600 transition-colors"
                  disabled={!code}
                >
                  <Download size={16} className="inline mr-1" />
                  Download
                </button>
                <button
                  onClick={() => setActiveTab('preview')}
                  className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors"
                  disabled={!output}
                >
                  <Play size={16} className="inline mr-1" />
                  View Preview
                </button>
                <button
                  onClick={() => setActiveTab('editor')}
                  className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors"
                >
                  <Sparkles size={16} className="inline mr-1" />
                  Edit Code
                </button>
              </div>
            </div>
            <div className="flex-1 bg-gray-900 overflow-auto">
              {code ? (
                <div className="h-full">
                  <div className="flex items-center justify-between p-2 bg-gray-800 border-b border-gray-700">
                    <span className="text-xs text-gray-400">App.jsx</span>
                    <span className="text-xs text-gray-400">{code.length} characters</span>
                  </div>
                  <div className="h-full p-4 text-sm text-gray-100 font-mono leading-relaxed overflow-auto bg-gray-900">
                    <div
                      className="language-javascript"
                      dangerouslySetInnerHTML={{ __html: formatCodeWithLineNumbers(code) }}
                    />
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <Code size={48} className="mx-auto mb-4" />
                    <p>No code generated yet</p>
                    <p className="text-sm mt-2">Generate code in the chat to view it here</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Editor Panel */}
        {activeTab === 'editor' && (
          <div className="flex-1 flex flex-col">
            <div className="bg-gray-800 border-b border-gray-700 p-2 flex items-center justify-between">
              <span className="text-gray-400 text-sm">JavaScript/React Editor</span>
              <div className="flex space-x-2">
                <button
                  onClick={copyCode}
                  className="bg-gray-700 text-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-600 transition-colors"
                >
                  <Copy size={16} className="inline mr-1" />
                  Copy
                </button>
                <button
                  onClick={downloadCode}
                  className="bg-gray-700 text-gray-300 px-3 py-1 rounded text-sm hover:bg-gray-600 transition-colors"
                >
                  <Download size={16} className="inline mr-1" />
                  Download
                </button>
                <button
                  onClick={validateCodeHandler}
                  className="bg-yellow-600 text-white px-3 py-1 rounded text-sm hover:bg-yellow-700 transition-colors"
                >
                  <Code size={16} className="inline mr-1" />
                  Validate
                </button>
                <button
                  onClick={runCode}
                  className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors"
                >
                  <Play size={16} className="inline mr-1" />
                  Run
                </button>
              </div>
            </div>
            <div ref={monacoRef} className="flex-1"></div>
          </div>
        )}

        {/* Preview Panel */}
        {activeTab === 'preview' && (
          <div className="flex-1 flex flex-col">
            <div className="bg-gray-800 border-b border-gray-700 p-2 flex items-center justify-between">
              <span className="text-gray-400 text-sm">Live Preview</span>
              <div className="flex space-x-2">
                {(output.includes('Execution Error:') || output.includes("Can't find variable")) && (
                  <>
                    <button
                      onClick={() => setActiveTab('codeview')}
                      className="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700 transition-colors"
                    >
                      <Code size={12} className="inline mr-1" />
                      View Code
                    </button>
                    <button
                      onClick={() => forceErrorCorrection("Fix the current error", lastError || "Fix the execution error")}
                      className="bg-red-600 text-white px-2 py-1 rounded text-xs hover:bg-red-700 transition-colors"
                      disabled={isLoading}
                    >
                      🔧 Auto-Fix Error
                    </button>
                  </>
                )}
                <button
                  onClick={() => window.open('data:text/html;charset=utf-8,' + encodeURIComponent(output), '_blank')}
                  className="bg-gray-700 text-gray-300 px-2 py-1 rounded text-xs hover:bg-gray-600 transition-colors"
                  disabled={!output}
                >
                  Open in New Tab
                </button>
                <button
                  onClick={() => setOutput('')}
                  className="bg-gray-700 text-gray-300 px-2 py-1 rounded text-xs hover:bg-gray-600 transition-colors"
                >
                  Clear
                </button>
              </div>
            </div>
            <div className="flex-1 bg-white overflow-hidden">
              {output ? (
                <iframe
                  srcDoc={output}
                  className="w-full h-full border-0"
                  sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"
                  title="Code Preview"
                />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <Play size={48} className="mx-auto mb-4" />
                    <p>Generate code to see live preview</p>
                    <p className="text-sm mt-2">Your app will automatically appear here</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MonacoChatbot;