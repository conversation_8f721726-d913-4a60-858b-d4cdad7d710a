import { GoogleGenAI } from '@google/genai';
import { action, internalMutation } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// ============================================================================
// CONFIGURATION VARIABLES
// ============================================================================

// API Keys - You should move these to environment variables in production
const GEMINI_API_KEY = "AIzaSyBaDWf_3ly9xZM26kYM3qppNpbOqwuiaTE";
const model = "gemini-2.5-flash";

// Configure the AI client
const ai = new GoogleGenAI({ apiKey: GEMINI_API_KEY });

// ============================================================================
// GLOBAL CHAT SESSION MANAGEMENT
// ============================================================================

let globalChat: any = null;
let chatBusy = false;
let requestQueue: Array<{
  resolve: (value: any) => void;
  reject: (error: any) => void;
  operation: () => Promise<any>;
}> = [];

// Configuration for Monaco chatbot
const config = {
  // No function calling needed for Monaco chatbot, just pure text generation
  generationConfig: {
    temperature: 0.7,
    topP: 0.8,
    topK: 40,
    maxOutputTokens: 8192,
  }
};

// Error correction configuration - Updated for better performance
const ERROR_CORRECTION_CONFIG = {
  MAX_ITERATIONS: 5,           // Maximum number of error correction attempts
  GUARANTEED_ITERATIONS: 2,    // Always try at least this many iterations
  CONFIDENCE_THRESHOLD: 7      // Minimum confidence (1-10) to stop iterating
};

// Initialize or reset the global chat session
function initializeGlobalChat() {
  console.log("[MONACO CHAT] Initializing global chat session");
  globalChat = ai.chats.create({
    model: model,
    config: config as any
  });
}

// Process the request queue
async function processQueue() {
  if (chatBusy || requestQueue.length === 0) {
    return;
  }

  chatBusy = true;
  const { resolve, reject, operation } = requestQueue.shift()!;

  try {
    const result = await operation();
    resolve(result);
  } catch (error) {
    reject(error);
  } finally {
    chatBusy = false;
    // Process next item in queue
    if (requestQueue.length > 0) {
      setTimeout(() => processQueue(), 100);
    }
  }
}

// Queue chat operations to prevent concurrent access
function queueChatOperation<T>(operation: () => Promise<T>): Promise<T> {
  return new Promise((resolve, reject) => {
    requestQueue.push({
      resolve,
      reject,
      operation
    });

    // Start processing if not already busy
    if (!chatBusy) {
      setTimeout(() => processQueue(), 0);
    }
  });
}

// Safe chat message sender
async function sendChatMessage(message: string): Promise<any> {
  return queueChatOperation(async () => {
    if (!globalChat) {
      initializeGlobalChat();
    }

    try {
      console.log(`[MONACO CHAT] Sending message: ${message.substring(0, 100)}...`);

      const response = await globalChat.sendMessage({
        message: message
      });

      return response;
    } catch (error) {
      console.error(`[MONACO CHAT] Error sending message:`, error);
      throw error;
    }
  });
}

// ============================================================================
// TYPES
// ============================================================================



// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Analyze generated code to extract file structure and dependencies
 */
async function analyzeGeneratedCode(code: string): Promise<{
  files: Array<{name: string; content: string; type: string}>;
  dependencies: string[];
}> {
  // Extract imports and dependencies
  const importRegex = /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g;
  const dependencies = new Set<string>();
  let match;

  while ((match = importRegex.exec(code)) !== null) {
    const dep = match[1];
    if (!dep.startsWith('./') && !dep.startsWith('../')) {
      dependencies.add(dep);
    }
  }

  // For now, treat as single file - could be enhanced to split into multiple files
  const files = [
    {
      name: 'App.jsx',
      content: code,
      type: 'javascript'
    }
  ];

  return {
    files,
    dependencies: Array.from(dependencies)
  };
}

/**
 * Execute code safely in a sandboxed environment
 */
async function executeCodeSafely(code: string): Promise<string> {
  // This is a placeholder for server-side code execution
  // In a real implementation, you might use a sandboxed environment
  // For now, we'll return a success message with code length info
  return `Code is ready for execution in the browser environment (${code.length} characters)`;
}

// ============================================================================
// MAIN ACTIONS
// ============================================================================

/**
 * Generate code based on user prompt using Gemini AI with enhanced capabilities
 */
export const generateAndExecuteCode = action({
  args: {
    prompt: v.string(),
    conversationHistory: v.optional(v.array(v.object({
      role: v.union(v.literal("user"), v.literal("assistant")),
      content: v.string()
    }))),
    sessionId: v.optional(v.string()),
    autoExecute: v.optional(v.boolean()),
  },
  handler: async (ctx, args): Promise<{
    code: string;
    htmlPreview: string;
    explanation?: string;
    files?: Array<{name: string; content: string; type: string}>;
    dependencies?: string[];
    executionResult?: string;
  }> => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    try {
      // Build conversation context
      let conversationContext = "";
      if (args.conversationHistory && args.conversationHistory.length > 0) {
        conversationContext = args.conversationHistory
          .map(msg => `${msg.role}: ${msg.content}`)
          .join('\n');
        conversationContext += '\n\n';
      }

      // Create the enhanced prompt for code generation like Vercel v0
      const systemPrompt = `You are an expert web development assistant that creates complete, production-ready web applications like Vercel v0.

Your task is to generate clean, functional, and complete code based on user requests. Follow these guidelines:

currently limit your jenerated code to pure javascript and html

. **Complete Applications**: Create full, working applications that can run immediately in a browser

. **Interactivity**: Include proper state management, event handlers, and user interactions
. **Responsiveness**: Make all components fully responsive and mobile-friendly
. **Accessibility**: Include proper ARIA labels, semantic HTML, and keyboard navigation
. **Error Handling**: Include comprehensive error handling and loading states
. **Real Functionality**: Create working features, not just UI mockups

**Technical Requirements:**
- Use modern JavaScript/TypeScript syntax
- Create a main 'App' component that can be rendered immediately
- Include all necessary state management with useState, useEffect, etc.
- Use proper component composition and reusable components
- Include realistic data and functionality (don't use placeholder text)
- Make it visually appealing with proper spacing, colors, and typography
- Add smooth animations and transitions where appropriate

**Output Format**:
- Provide ONLY the complete JavaScript/React code
- No explanations, markdown formatting, or code blocks
- Make sure the code is immediately executable
- Include inline comments for complex logic
- Use modern ES6+ syntax and React patterns

**Previous Conversation:**
${conversationContext}

**Current Request:** ${args.prompt}

**CRITICAL OUTPUT FORMAT:**
- Return ONLY valid JavaScript code as PLAIN TEXT
- Start with "function App() {"
- End with the closing "}" of the App function
- NO markdown, NO explanations, NO extra text
- NO imports or exports
- NO CSS styling, NO color codes, NO HTML attributes
- NO syntax highlighting markup or color information
- Must be syntactically correct JavaScript
- ALL variables must be defined before use
- Use React.useState, not just useState
- NO undefined variables like 'expression', 'todo', 'task', 'placeholder'
- Replace any undefined variables with actual values
- Output should be copyable directly into a JavaScript file

**CRITICAL OUTPUT FORMAT:**
- Provide ONLY plain text JavaScript code
- NO syntax highlighting markup or color codes
- NO HTML tags or styling attributes in the code
- NO markdown formatting or code blocks
- Just raw, executable JavaScript code

given below is the format you should exactly follow

\\\`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated App Preview</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #ffffff;
        }
        .error {
            color: #dc2626;
            background: #fef2f2;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 1rem;
            border: 1px solid #fecaca;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-size: 1.125rem;
            color: #6b7280;
        }
        .debug {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #000;
            color: #fff;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">Loading application...</div>
    </div>

    <script>
        // Debug logging
        console.log('Starting application...');
        console.log('React available:', typeof React !== 'undefined');
        console.log('ReactDOM available:', typeof ReactDOM !== 'undefined');
        console.log('Babel available:', typeof Babel !== 'undefined');

        // Error handling for script loading
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
            document.getElementById('root').innerHTML =
                '<div class="error">Script Error: ' + e.message + '<br>File: ' + e.filename + '<br>Line: ' + e.lineno + '</div>';
        });

        // Wait for all scripts to load
        function waitForDependencies() {
            if (typeof React !== 'undefined' &&
                typeof ReactDOM !== 'undefined' &&
                typeof Babel !== 'undefined') {
                console.log('All dependencies loaded, executing code...');
                executeCode();
            } else {
                console.log('Waiting for dependencies...');
                setTimeout(waitForDependencies, 100);
            }
        }

        function executeCode() {
            try {
                // Transform and execute the code
                const transformedCode = Babel.transform(\`
                    // Provide React hooks and common utilities
                    const { useState, useEffect, useRef, useCallback, useMemo, Fragment } = React;

                    // Provide common variables that might be referenced
                    const expression = "sample expression";
                    const placeholder = "placeholder text";
                    const todo = "TODO";
                    const task = "sample task";
                    const item = "sample item";
                    const data = "sample data";
                    const value = "sample value";
                    const text = "sample text";

                    // Provide safe fallbacks for common undefined variables
                    window.expression = window.expression || "sample expression";
                    window.placeholder = window.placeholder || "placeholder text";
                    window.todo = window.todo || "TODO";
                    window.task = window.task || "sample task";
                    window.item = window.item || "sample item";
                    window.data = window.data || "sample data";
                    window.value = window.value || "sample value";
                    window.text = window.text || "sample text";

                    // Wrap in try-catch for better error handling
                    try {
                        //you should create and app functional component accoring to users request here,modify the this content appropriatly to support content here

                        // Render the App component
                        const root = ReactDOM.createRoot(document.getElementById('root'));
                        if (typeof App !== 'undefined') {
                            root.render(React.createElement(App));
                        } else {
                            root.render(React.createElement('div', {
                                className: 'flex items-center justify-center min-h-screen bg-gray-50'
                            }, React.createElement('div', {
                                className: 'text-center p-8'
                            }, [
                                React.createElement('div', {
                                    key: 'success',
                                    className: 'text-green-600 text-xl mb-2'
                                }, '✅ Code executed successfully!'),
                                React.createElement('div', {
                                    key: 'message',
                                    className: 'text-gray-600'
                                }, 'Define an App component to see the application.')
                            ])));
                        }
                    } catch (codeError) {
                        console.error('Code execution error:', codeError);
                        const root = ReactDOM.createRoot(document.getElementById('root'));
                        root.render(React.createElement('div', {
                            className: 'error'
                        }, 'Code Error: ' + codeError.message));
                    }
                \`, {
                    presets: ['react']
                }).code;

                console.log('Code transformed successfully');
                eval(transformedCode);

            } catch (error) {
                console.error('Execution error:', error);
                document.getElementById('root').innerHTML =
                    '<div class="error"><strong>Execution Error:</strong><br>' +
                    error.message + '<br><br><strong>Stack:</strong><br>' +
                    (error.stack || 'No stack trace available') + '</div>';
            }
        }

        // Start the process
        waitForDependencies();
    </script>
</body>
</html>\\\`

Create a complete, working application now:`;

      // Generate code using the global chat
      const response = await sendChatMessage(systemPrompt);
      const rawCode = response.text || "";

      console.log(`[MONACO CHAT] Raw response length: ${rawCode.length} characters`);

      if (!rawCode.trim()) {
        throw new Error("No code was generated. Please try rephrasing your request.");
      }

      // Extract and clean the JavaScript code
      console.log(rawCode);
      const generatedCode = extractAndCleanJavaScriptCode(rawCode);
      console.log(`[MONACO CHAT] Cleaned code length: ${generatedCode.length} characters`);

      // Analyze the generated code to extract dependencies and structure
      const codeAnalysis = await analyzeGeneratedCode(generatedCode);
      

      // Store the interaction in the database
      if (args.sessionId) {
        await ctx.runMutation(internal.monacoActions.storeCodeGeneration, {
          userId,
          sessionId: args.sessionId,
          prompt: args.prompt,
          generatedCode,
          timestamp: Date.now(),
        });
      }

      // Auto-execute if requested
      let executionResult;
      if (args.autoExecute) {
        try {
          executionResult = await executeCodeSafely(generatedCode);
          console.log(`[MONACO CHAT] Code executed successfully`);
        } catch (execError) {
          console.error(`[MONACO CHAT] Code execution failed:`, execError);
          executionResult = `Execution failed: ${execError instanceof Error ? execError.message : 'Unknown error'}`;
        }
      }

      // Generate HTML preview
      const htmlPreview = generateHTMLPreview(generatedCode);

      return {
        code: generatedCode,
        htmlPreview: htmlPreview,
        explanation: "Complete application generated successfully",
        files: codeAnalysis.files,
        dependencies: codeAnalysis.dependencies,
        executionResult
      };

    } catch (error) {
      console.error("Error generating code:", error);
      throw new Error(`Failed to generate code: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});




/**
 * Generate HTML preview for immediate code execution
 */
function generateHTMLPreview(code: string): string {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated App Preview</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }

        .error {
            color: #dc2626;
            background: #fef2f2;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 1rem;
            border: 1px solid #fecaca;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-size: 1.125rem;
            color: #6b7280;
        }
        .debug {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #000;
            color: #fff;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            max-width: 300px;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">Loading application...</div>
    </div>

    <script type="text/babel">
        // Global error handler
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
            const root = document.getElementById('root');
            if (root) {
                root.innerHTML = \`<div class="error">Error: \${e.error?.message || 'Unknown error occurred'}</div>\`;
            }
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled promise rejection:', e.reason);
            const root = document.getElementById('root');
            if (root) {
                root.innerHTML = \`<div class="error">Promise Error: \${e.reason?.message || 'Unknown promise rejection'}</div>\`;
            }
        });

        // Debug function
        function debugLog(message) {
            console.log('[DEBUG]', message);
        }

        // Wait for dependencies to load
        function waitForDependencies() {
            if (typeof React === 'undefined' || typeof ReactDOM === 'undefined' || typeof Babel === 'undefined') {
                setTimeout(waitForDependencies, 100);
                return;
            }

            try {
                // Define common variables that might be used in generated code
                const expression = "sample expression";
                const placeholder = "placeholder text";
                const todo = "TODO";
                const task = "sample task";
                const item = "sample item";
                const data = "sample data";
                const value = "sample value";
                const text = "sample text";

                // Provide safe fallbacks for common undefined variables
                window.expression = window.expression || "sample expression";
                window.placeholder = window.placeholder || "placeholder text";
                window.todo = window.todo || "TODO";
                window.task = window.task || "sample task";
                window.item = window.item || "sample item";
                window.data = window.data || "sample data";
                window.value = window.value || "sample value";
                window.text = window.text || "sample text";

                // Wrap in try-catch for better error handling
                try {
                    const transformedCode = Babel.transform(\`
                        ${code}

                        // Render the app
                        const root = ReactDOM.createRoot(document.getElementById('root'));
                        root.render(React.createElement(App));
                    \`, {
                        presets: ['react']
                    });

                    // Execute the transformed code
                    eval(transformedCode.code);

                    debugLog('App rendered successfully');
                } catch (error) {
                    console.error('Code execution error:', error);
                    const root = document.getElementById('root');
                    if (root) {
                        root.innerHTML = \`<div class="error">
                            <strong>Code Execution Error:</strong><br>
                            \${error.message}<br><br>
                            <strong>Stack:</strong><br>
                            \${error.stack || 'No stack trace available'}
                        </div>\`;
                    }
                }
            } catch (error) {
                console.error('Setup error:', error);
                const root = document.getElementById('root');
                if (root) {
                    root.innerHTML = \`<div class="error">Setup Error: \${error.message}</div>\`;
                }
            }
        }

        // Start the process
        waitForDependencies();
    </script>
</body>
</html>`;
}




/**
 * Extract and clean JavaScript code from AI response
 */
function extractAndCleanJavaScriptCode(rawResponse: string): string {
  console.log(`[MONACO CODE EXTRACTION] Processing raw response: ${rawResponse.substring(0, 200)}...`);

  // Debug: Log if we detect syntax highlighting patterns
  if (rawResponse.includes('color:') || rawResponse.includes('#569cd6') || rawResponse.includes('"color')) {
    console.log(`[MONACO CODE EXTRACTION] WARNING: Detected syntax highlighting in AI response!`);
    console.log(`[MONACO CODE EXTRACTION] Sample problematic content: ${rawResponse.substring(0, 500)}`);
  }

  let cleanedCode = rawResponse.trim();

  // Remove markdown code blocks
  cleanedCode = cleanedCode.replace(/```(?:javascript|jsx|js)?\n?/g, '');
  cleanedCode = cleanedCode.replace(/```\n?/g, '');

  // Remove CSS color styling that sometimes gets included in AI responses
  cleanedCode = cleanedCode.replace(/"color:\s*#[0-9a-fA-F]{6}">/g, '');
  cleanedCode = cleanedCode.replace(/color:\s*#[0-9a-fA-F]{6}/g, '');
  cleanedCode = cleanedCode.replace(/"color:\s*#[0-9a-fA-F]{3}">/g, '');
  cleanedCode = cleanedCode.replace(/color:\s*#[0-9a-fA-F]{3}/g, '');

  // Remove specific syntax highlighting patterns that Gemini generates
  cleanedCode = cleanedCode.replace(/"color:\s*#[0-9a-fA-F]{6};">/g, '');
  cleanedCode = cleanedCode.replace(/"color:\s*#[0-9a-fA-F]{3};">/g, '');
  cleanedCode = cleanedCode.replace(/color:\s*#[0-9a-fA-F]{6};/g, '');
  cleanedCode = cleanedCode.replace(/color:\s*#[0-9a-fA-F]{3};/g, '');

  // Remove any remaining HTML-like color attributes
  cleanedCode = cleanedCode.replace(/style="[^"]*color[^"]*"/g, '');
  cleanedCode = cleanedCode.replace(/class="[^"]*color[^"]*"/g, '');

  // Remove syntax highlighting artifacts that might appear
  cleanedCode = cleanedCode.replace(/"[^"]*color[^"]*">/g, '');
  cleanedCode = cleanedCode.replace(/<[^>]*color[^>]*>/g, '');
  cleanedCode = cleanedCode.replace(/\{[^}]*color[^}]*\}/g, '');

  // Remove any remaining HTML tags or attributes
  cleanedCode = cleanedCode.replace(/<\/?[^>]+>/g, '');
  cleanedCode = cleanedCode.replace(/\s+style="[^"]*"/g, '');
  cleanedCode = cleanedCode.replace(/\s+class="[^"]*"/g, '');

  // Additional comprehensive cleaning for syntax highlighting artifacts
  // Remove any pattern that looks like: "color: #xxxxxx;">
  cleanedCode = cleanedCode.replace(/"color:\s*#[0-9a-fA-F]{3,6};"?>/g, '');
  // Remove any pattern that looks like: color: #xxxxxx;
  cleanedCode = cleanedCode.replace(/color:\s*#[0-9a-fA-F]{3,6};?/g, '');
  // Remove any quoted color attributes
  cleanedCode = cleanedCode.replace(/"color:\s*#[0-9a-fA-F]{3,6};?"[^>]*>/g, '');
  // Remove standalone color declarations
  cleanedCode = cleanedCode.replace(/^\s*color:\s*#[0-9a-fA-F]{3,6};?\s*$/gm, '');

  // Remove any remaining syntax highlighting markup patterns
  cleanedCode = cleanedCode.replace(/\s*"[^"]*color[^"]*"[^>]*>/g, '');
  cleanedCode = cleanedCode.replace(/\s*color[^;]*;[^>]*>/g, '');

  // Clean up any malformed lines that start with color declarations
  cleanedCode = cleanedCode.replace(/^\s*"color[^"]*"[^>]*>\s*/gm, '');

  // Remove any remaining HTML-like syntax highlighting tags
  cleanedCode = cleanedCode.replace(/<span[^>]*color[^>]*>/gi, '');
  cleanedCode = cleanedCode.replace(/<\/span>/gi, '');
  cleanedCode = cleanedCode.replace(/<div[^>]*color[^>]*>/gi, '');
  cleanedCode = cleanedCode.replace(/<\/div>/gi, '');

  // Remove any leading/trailing explanatory text
  const codePatterns = [
    // Look for function App() pattern (most specific first)
    /function\s+App\s*\(\s*\)\s*\{[\s\S]*?\n\}/,
    /function\s+App\s*\(\s*\)\s*\{[\s\S]*\}/,
    // Look for const App = () => pattern
    /const\s+App\s*=\s*\(\s*\)\s*=>\s*\{[\s\S]*\}/,
    // Look for arrow function pattern (last resort)
    /\(\s*\)\s*=>\s*\{[\s\S]*\}/
  ];

  for (const pattern of codePatterns) {
    const match = cleanedCode.match(pattern);
    if (match) {
      cleanedCode = match[0];
      break;
    }
  }

  // If no pattern matched, try to find the largest code block
  if (!cleanedCode.includes('function App') && !cleanedCode.includes('const App')) {
    // Split by lines and find the largest continuous block of code
    const lines = cleanedCode.split('\n');
    let bestEnd = lines.length;
    let currentStart = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Skip empty lines and comments
      if (!line || line.startsWith('//') || line.startsWith('*') || line.startsWith('/*')) {
        continue;
      }

      // If this looks like explanatory text, skip it
      if (line.includes('Here') || line.includes('This') || line.includes('The code') ||
          line.includes('I\'ve') || line.includes('You can') || line.includes('Note:')) {
        currentStart = i + 1;
        continue;
      }

      // If we find a function declaration, this is likely the start
      if (line.includes('function') || line.includes('const') || line.includes('let') || line.includes('var')) {
        currentStart = i;
        break;
      }
    }

    if (currentStart < lines.length) {
      cleanedCode = lines.slice(currentStart, bestEnd).join('\n');
    }
  }

  // Ensure we have a proper App function
  if (!cleanedCode.includes('function App')) {
    // Try to wrap the code in a function App if it's just JSX or component logic
    if (cleanedCode.includes('return') || cleanedCode.includes('useState') || cleanedCode.includes('<')) {
      cleanedCode = `function App() {
${cleanedCode}
}`;
    } else {
      // If all else fails, create a basic structure
      cleanedCode = `function App() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-800">Generated App</h1>
        <p className="text-gray-600 mt-2">Code generation in progress...</p>
      </div>
    </div>
  );
}`;
    }
  }

  // Final aggressive cleanup for any remaining syntax highlighting artifacts
  // This specifically targets patterns like: "color: #569cd6;">const
  cleanedCode = cleanedCode.replace(/^[^a-zA-Z]*"color:[^"]*">[^a-zA-Z]*/gm, '');
  cleanedCode = cleanedCode.replace(/^[^a-zA-Z]*color:[^;]*;[^a-zA-Z]*/gm, '');

  // Remove any lines that are purely syntax highlighting markup
  const lines = cleanedCode.split('\n');
  const cleanLines = lines.filter(line => {
    const trimmed = line.trim();
    // Skip lines that are purely color/styling markup
    if (trimmed.match(/^"?color:\s*#[0-9a-fA-F]{3,6}[";>]/)) return false;
    if (trimmed.match(/^<[^>]*color[^>]*>$/)) return false;
    if (trimmed.match(/^"[^"]*color[^"]*"[^>]*>$/)) return false;
    return true;
  });
  cleanedCode = cleanLines.join('\n');

  // Final cleanup and validation
  cleanedCode = cleanedCode.trim();

  // Basic syntax validation
  if (!cleanedCode.includes('function App')) {
    throw new Error("Generated code does not contain a proper App function");
  }

  // Check for basic JavaScript syntax
  const openBraces = (cleanedCode.match(/\{/g) || []).length;
  const closeBraces = (cleanedCode.match(/\}/g) || []).length;

  if (openBraces !== closeBraces) {
    console.log(`[MONACO CODE EXTRACTION] Brace mismatch: ${openBraces} open, ${closeBraces} close`);
    // Try to fix simple brace issues
    if (openBraces > closeBraces) {
      cleanedCode += '\n' + '}'.repeat(openBraces - closeBraces);
    }
  }

  // Check for common problematic patterns and fix them
  cleanedCode = fixCommonCodeIssues(cleanedCode);

  // Pre-validate for undefined variables
  const undefinedVars = findUndefinedVariables(cleanedCode);
  if (undefinedVars.length > 0) {
    console.log(`[MONACO CODE EXTRACTION] Found undefined variables: ${undefinedVars.join(', ')}`);
    // Try to fix them automatically
    cleanedCode = fixUndefinedVariables(cleanedCode, undefinedVars);
  }

  console.log(`[MONACO CODE EXTRACTION] Final cleaned code: ${cleanedCode.substring(0, 200)}...`);
  return cleanedCode;
}

/**
 * Fix common code issues that cause runtime errors
 * Updated: Conservative approach to avoid breaking valid code
 */
function fixCommonCodeIssues(code: string): string {
  let fixedCode = code;

  console.log(`[MONACO CODE FIXING] Starting code fixes...`);

  // Fix 1: Only replace undefined variables in specific contexts (not all occurrences)
  // Only replace when they appear as standalone variables, not as properties or in valid contexts
  const undefinedVariablePatterns = [
    // Only replace if it's a standalone variable reference, not part of a property or method
    { pattern: /\{expression\}/g, replacement: '{"sample expression"}' },
    { pattern: /\{undefined\}/g, replacement: '{""}' },
    { pattern: /\{null\}/g, replacement: '{""}' },
  ];

  undefinedVariablePatterns.forEach(({ pattern, replacement }) => {
    if (pattern.test(fixedCode)) {
      console.log(`[MONACO CODE FIXING] Replacing ${pattern} with ${replacement}`);
      fixedCode = fixedCode.replace(pattern, replacement);
    }
  });

  // Fix 2: Remove problematic imports/exports
  fixedCode = fixedCode.replace(/import\s+.*?;?\n/g, '');
  fixedCode = fixedCode.replace(/export\s+.*?;?\n/g, '');

  // Fix 3: Ensure proper function structure only if clearly missing
  if (!fixedCode.includes('return') && fixedCode.includes('<') && fixedCode.includes('>')) {
    console.log(`[MONACO CODE FIXING] Adding return statement`);
    // Find the JSX part and wrap it in return
    const jsxMatch = fixedCode.match(/<[^>]+>[\s\S]*<\/[^>]+>/);
    if (jsxMatch) {
      const jsx = jsxMatch[0];
      fixedCode = fixedCode.replace(jsx, `return (\n    ${jsx}\n  );`);
    }
  }

  console.log(`[MONACO CODE FIXING] Code fixes completed`);
  return fixedCode;
}

/**
 * Find undefined variables in code - much more conservative approach
 */
function findUndefinedVariables(code: string): string[] {
  const undefinedVars: string[] = [];

  // Only look for variables that are clearly undefined in JSX expressions
  const jsxUndefinedPatterns = [
    /\{expression\}/g,
    /\{undefined\}/g,
    /\{null\}/g,
  ];

  jsxUndefinedPatterns.forEach(pattern => {
    const matches = code.match(pattern);
    if (matches) {
      matches.forEach(match => {
        const varName = match.replace(/[{}]/g, '');
        if (!undefinedVars.includes(varName)) {
          undefinedVars.push(varName);
        }
      });
    }
  });

  return undefinedVars;
}

/**
 * Fix undefined variables in code - only fix JSX expressions
 */
function fixUndefinedVariables(code: string, undefinedVars: string[]): string {
  let fixedCode = code;

  const replacements: Record<string, string> = {
    'expression': '"sample expression"',
    'undefined': '""',
    'null': '""',
  };

  undefinedVars.forEach(varName => {
    const replacement = replacements[varName] || `"${varName}"`;
    // Only replace in JSX expressions, not everywhere
    const jsxPattern = new RegExp(`\\{${varName}\\}`, 'g');
    fixedCode = fixedCode.replace(jsxPattern, `{${replacement}}`);
    console.log(`[MONACO CODE FIXING] Replaced '{${varName}}' with {${replacement}}`);
  });

  return fixedCode;
}

/**
 * Validate generated code for common errors
 */
async function validateGeneratedCode(code: string, iteration: number): Promise<{
  hasErrors: boolean;
  error: string;
  suggestedFix: string;
}> {
  // Basic syntax validation
  const commonErrors = [
    { pattern: /Can't find variable:\s*(\w+)/i, error: "Undefined variable detected", fix: "Define all variables before use or replace with actual values" },
    { pattern: /is not a function/i, error: "Malformed function declaration", fix: "Ensure proper function syntax: function App() { ... }" },
    { pattern: /\bexpression\b/i, error: "Undefined variable 'expression'", fix: "Replace 'expression' with actual value or string" },
    { pattern: /\btodo\b/i, error: "Code contains 'todo' placeholder", fix: "Replace todo with actual implementation" },
    { pattern: /\btask\b/i, error: "Code contains 'task' placeholder", fix: "Replace task with actual implementation" },
    { pattern: /\bundefined\s+variable/i, error: "Undefined variable detected", fix: "Define all variables before use" },
    { pattern: /\blet\s+todo\b/i, error: "Variable 'todo' is not defined", fix: "Replace 'todo' with proper variable name" },
    { pattern: /\blet\s+task\b/i, error: "Variable 'task' is not defined", fix: "Replace 'task' with proper variable name" },
    { pattern: /\bconst\s+todo\b/i, error: "Variable 'todo' is not defined", fix: "Replace 'todo' with proper variable name" },
    { pattern: /\bvar\s+todo\b/i, error: "Variable 'todo' is not defined", fix: "Replace 'todo' with proper variable name" },
    { pattern: /import\s+/i, error: "Import statements not allowed", fix: "Remove import statements, use React.useState instead" },
    { pattern: /export\s+/i, error: "Export statements not needed", fix: "Remove export statements" },
    { pattern: /\{todo\}/i, error: "Undefined variable 'todo' in JSX", fix: "Replace {todo} with actual variable or value" },
    { pattern: /\{expression\}/i, error: "Undefined variable 'expression' in JSX", fix: "Replace {expression} with actual variable or value" },
    { pattern: /onClick=\{todo\}/i, error: "Undefined function 'todo' in onClick", fix: "Replace with actual function" },
    { pattern: /function\s*\(\s*\)/i, error: "Anonymous function instead of App function", fix: "Use 'function App()' instead" },
  ];

  for (const check of commonErrors) {
    if (check.pattern.test(code)) {
      return {
        hasErrors: true,
        error: check.error,
        suggestedFix: check.fix
      };
    }
  }

  // Check for App component
  if (!code.includes('function App(')) {
    return {
      hasErrors: true,
      error: "No App component found",
      suggestedFix: "Define a function component named 'App'"
    };
  }

  // Check for common undefined variables
  const undefinedVarPatterns = [
    /\b(\w+)\s*is\s*not\s*defined/i,
    /Can't\s*find\s*variable:\s*(\w+)/i,
    /ReferenceError:\s*(\w+)\s*is\s*not\s*defined/i
  ];

  for (const pattern of undefinedVarPatterns) {
    const match = code.match(pattern);
    if (match) {
      return {
        hasErrors: true,
        error: `Undefined variable: ${match[1]}`,
        suggestedFix: `Define the variable '${match[1]}' or replace with correct variable name`
      };
    }
  }

  // If we've tried multiple times and still have issues, be more lenient
  if (iteration > 3) {
    return {
      hasErrors: false,
      error: "",
      suggestedFix: ""
    };
  }

  return {
    hasErrors: false,
    error: "",
    suggestedFix: ""
  };
}

/**
 * Evaluate if error correction should continue (similar to search.ts)
 */
async function evaluateIfShouldContinue(
  originalPrompt: string,
  currentCode: string,
  currentError: string,
  errorHistory: Array<{iteration: number; error: string; fix: string}>,
  iteration: number
): Promise<{ shouldContinue: boolean; reasoning: string; confidence: number }> {
  const evaluationPrompt = `
Based on the code generation progress so far, determine if more iterations are needed to fix the errors and create a working React application.

**Original Request:** ${originalPrompt}

**Current Code Status:**
- Iteration: ${iteration}
- Current Error: ${currentError}
- Code Length: ${currentCode.length} characters

**Error History:**
${errorHistory.map(h => `${h.iteration}. ${h.error} -> ${h.fix}`).join('\n')}

**Current Code:**
\`\`\`javascript
${currentCode.substring(0, 1000)}${currentCode.length > 1000 ? '...' : ''}
\`\`\`

Consider:
1. Is the current error fixable with another iteration?
2. Are we making progress or stuck in a loop?
3. Is the code close to working?
4. Would another iteration likely succeed?

Provide your evaluation in this exact JSON format:
{
  "shouldContinue": true/false,
  "reasoning": "Brief explanation of why more iterations are or aren't needed",
  "confidence": 1-10 (how confident you are in this decision)
}

NEVER PROVIDE THE ANSWER IN ANY OTHER FORMAT.`;

  try {
    const response = await sendChatMessage(evaluationPrompt);
    const evaluationText = response.text || '';

    if (!evaluationText) {
      return { shouldContinue: false, reasoning: "No evaluation response", confidence: 5 };
    }

    const cleanedText = evaluationText.replace(/```json\n?|\n?```/g, '').trim();
    const evaluation = JSON.parse(cleanedText);

    return {
      shouldContinue: evaluation.shouldContinue && evaluation.confidence >= ERROR_CORRECTION_CONFIG.CONFIDENCE_THRESHOLD,
      reasoning: evaluation.reasoning || "No reasoning provided",
      confidence: evaluation.confidence || 5
    };
  } catch (error) {
    console.error("[MONACO ERROR CORRECTION] Failed to parse evaluation:", error);
    return { shouldContinue: false, reasoning: "Evaluation parsing failed", confidence: 1 };
  }
}




/**
 * Generate a simple test application to verify the system is working
 */
export const generateTestApp = action({
  args: {},
  handler: async (ctx): Promise<{
    code: string;
    htmlPreview: string;
    explanation: string;
  }> => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Simple test React component
    const testCode = `function App() {
  const [count, setCount] = React.useState(0);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-xl p-8 text-center">
        <h1 className="text-3xl font-bold text-gray-800 mb-4">
          🚀 Monaco Chatbot Test
        </h1>
        <p className="text-gray-600 mb-6">
          Click the button to test interactivity!
        </p>
        <div className="mb-6">
          <span className="text-4xl font-bold text-blue-600">{count}</span>
        </div>
        <button
          onClick={() => setCount(count + 1)}
          className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition-colors"
        >
          Click me!
        </button>
        <div className="mt-4 text-sm text-gray-500">
          System is working correctly ✅
        </div>
      </div>
    </div>
  );
}`;

    const htmlPreview = generateHTMLPreview(testCode);

    return {
      code: testCode,
      htmlPreview,
      explanation: "Test application generated successfully! The system is working correctly."
    };
  },
});

/**
 * Validate and analyze code for potential issues
 */
export const validateCode = action({
  args: {
    code: v.string(),
    sessionId: v.optional(v.string()),
  },
  handler: async (ctx, args): Promise<{ isValid: boolean; issues: string[]; suggestions: string[] }> => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    try {
      const validationPrompt = `You are a code review expert. Analyze the following JavaScript/React code for:

1. **Syntax Errors**: Check for any syntax issues
2. **Logic Issues**: Identify potential logical problems
3. **Best Practices**: Note any violations of React/JavaScript best practices
4. **Security Concerns**: Flag any potential security issues
5. **Performance Issues**: Identify potential performance problems

**Code to analyze:**
\`\`\`javascript
${args.code}
\`\`\`

**Response Format (JSON):**
{
  "isValid": boolean,
  "issues": ["list of specific issues found"],
  "suggestions": ["list of improvement suggestions"]
}

Provide only the JSON response:`;

      const response = await sendChatMessage(validationPrompt);
      const analysisText = response.text || "";

      console.log(`[MONACO CHAT] Validation response length: ${analysisText.length} characters`);

      if (!analysisText.trim()) {
        return {
          isValid: false,
          issues: ["Unable to analyze code - no response from AI"],
          suggestions: ["Please try again or check your code manually."]
        };
      }

      try {
        // Clean up the response text (remove markdown formatting if present)
        const cleanedText = analysisText.replace(/```json\n?|\n?```/g, '').trim();

        // Try to parse the JSON response
        const analysis = JSON.parse(cleanedText);

        console.log(`[MONACO CHAT] Validation result: ${analysis.isValid ? 'Valid' : 'Issues found'}`);

        return {
          isValid: analysis.isValid || false,
          issues: analysis.issues || [],
          suggestions: analysis.suggestions || []
        };
      } catch (parseError) {
        console.error(`[MONACO CHAT] Failed to parse validation response:`, parseError);

        // Fallback if JSON parsing fails - try to extract useful info from text
        const isValid = !analysisText.toLowerCase().includes('error') &&
                       !analysisText.toLowerCase().includes('issue') &&
                       !analysisText.toLowerCase().includes('problem');

        return {
          isValid,
          issues: isValid ? [] : ["Code analysis completed, but detailed issues could not be parsed."],
          suggestions: [`Raw analysis: ${analysisText.substring(0, 200)}...`]
        };
      }

    } catch (error) {
      console.error("Error validating code:", error);
      return {
        isValid: false,
        issues: [`Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        suggestions: ["Please check your code manually for any issues."]
      };
    }
  },
});

/**
 * Get code generation history for a user
 */
export const getCodeHistory = action({
  args: {
    sessionId: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args): Promise<any[]> => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    return await ctx.runMutation(internal.monacoActions.getUserCodeHistory, {
      userId,
      sessionId: args.sessionId,
      limit: args.limit || 20,
    });
  },
});

// ============================================================================
// INTERNAL MUTATIONS
// ============================================================================

/**
 * Store code generation interaction in the database
 */
export const storeCodeGeneration = internalMutation({
  args: {
    userId: v.id("users"),
    sessionId: v.string(),
    prompt: v.string(),
    generatedCode: v.string(),
    timestamp: v.number(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("monacoSessions", {
      userId: args.userId,
      sessionId: args.sessionId,
      prompt: args.prompt,
      generatedCode: args.generatedCode,
      timestamp: args.timestamp,
      createdAt: Date.now(),
    });
  },
});

/**
 * Get user's code generation history
 */
export const getUserCodeHistory = internalMutation({
  args: {
    userId: v.id("users"),
    sessionId: v.optional(v.string()),
    limit: v.number(),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("monacoSessions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId));

    if (args.sessionId) {
      query = query.filter((q) => q.eq(q.field("sessionId"), args.sessionId));
    }

    return await query
      .order("desc")
      .take(args.limit);
  },
});
